/** 判断是否是OAuth2APP环境 */
export function isOAuth2AppEnv() {
    // #ifdef H5
    // return /wxwork|dingtalk/i.test(navigator.userAgent)
    // #endif
    return false;
}

// 获取url中的参数
export const getUrlParams = (url) => {
    let result = {
        url: '',
        params: {}
    };
    let list = url.split('?');
    result.url = list[0];
    let params = list[1];
    if (params) {
        let list = params.split('&');
        list.forEach(ele => {
            let dic = ele.split('=');
            let label = dic[0];
            let value = dic[1];
            result.params[label] = value;
        });
    }
    return result;
};

/**
 * Base64编码方法
 * @param {string} str - 需要编码的字符串
 * @returns {string} - Base64编码后的字符串
 */
export const encryptByBase64 = (str) => {
    try {
        // 参数验证
        if (typeof str !== 'string') {
            console.warn('encryptByBase64: 参数必须是字符串类型');
            return '';
        }

        if (!str) {
            return '';
        }

        // #ifdef H5
        // H5环境使用浏览器原生btoa方法
        if (typeof window !== 'undefined' && window.btoa) {
            return window.btoa(unescape(encodeURIComponent(str)));
        }
        // #endif

        // #ifdef MP || APP-PLUS
        // 小程序和App环境使用uni.arrayBufferToBase64
        try {
            const buffer = new ArrayBuffer(str.length);
            const uint8Array = new Uint8Array(buffer);
            for (let i = 0; i < str.length; i++) {
                uint8Array[i] = str.charCodeAt(i);
            }
            return uni.arrayBufferToBase64(buffer);
        } catch (e) {
            console.error('encryptByBase64 uni.arrayBufferToBase64 error:', e);
            // 降级到手动实现
            return manualBase64Encode(str);
        }
        // #endif

        // 降级方案：手动实现Base64编码
        return manualBase64Encode(str);

    } catch (error) {
        console.error('encryptByBase64 error:', error);
        return '';
    }
};

/**
 * 手动实现Base64编码（降级方案）
 * @param {string} str - 需要编码的字符串
 * @returns {string} - Base64编码后的字符串
 */
const manualBase64Encode = (str) => {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/';
    let result = '';
    let i = 0;

    // 将字符串转换为UTF-8字节数组
    const utf8Bytes = [];
    for (let j = 0; j < str.length; j++) {
        const code = str.charCodeAt(j);
        if (code < 0x80) {
            utf8Bytes.push(code);
        } else if (code < 0x800) {
            utf8Bytes.push(0xc0 | (code >> 6));
            utf8Bytes.push(0x80 | (code & 0x3f));
        } else if (code < 0xd800 || code >= 0xe000) {
            utf8Bytes.push(0xe0 | (code >> 12));
            utf8Bytes.push(0x80 | ((code >> 6) & 0x3f));
            utf8Bytes.push(0x80 | (code & 0x3f));
        } else {
            // 代理对
            j++;
            const hi = code;
            const lo = str.charCodeAt(j);
            const codePoint = 0x10000 + (((hi & 0x3ff) << 10) | (lo & 0x3ff));
            utf8Bytes.push(0xf0 | (codePoint >> 18));
            utf8Bytes.push(0x80 | ((codePoint >> 12) & 0x3f));
            utf8Bytes.push(0x80 | ((codePoint >> 6) & 0x3f));
            utf8Bytes.push(0x80 | (codePoint & 0x3f));
        }
    }

    // Base64编码
    while (i < utf8Bytes.length) {
        const a = utf8Bytes[i++];
        const b = i < utf8Bytes.length ? utf8Bytes[i++] : 0;
        const c = i < utf8Bytes.length ? utf8Bytes[i++] : 0;

        const bitmap = (a << 16) | (b << 8) | c;

        result += chars.charAt((bitmap >> 18) & 63);
        result += chars.charAt((bitmap >> 12) & 63);
        result += i - 2 < utf8Bytes.length ? chars.charAt((bitmap >> 6) & 63) : '=';
        result += i - 1 < utf8Bytes.length ? chars.charAt(bitmap & 63) : '=';
    }

    return result;
};

/**
 * 获取URL中的参数
 * @param {string} url - 需要解析的URL字符串
 * @returns {Object} 包含URL和参数的对象，格式为{url: string, params: Object}
 * @description 将URL字符串解析为基础URL和参数对象，参数以键值对形式存储
 * @example
 * // 返回 {url: 'https://example.com', params: {id: '123', name: 'test'}}
 * getUrlParams('https://example.com?id=123&name=test')
 */

export function imageWatermark(pFile, pText,pText1='') {
  return new Promise((resolve, reject) => {
    const isImage = pFile.type.indexOf("image");
    if (isImage == -1) {
      return reject("请上传图片");
    } else {
      const reader = new FileReader();
      reader.readAsDataURL(pFile);
      reader.onload = () => {
        const mImg = new Image();
        mImg.setAttribute("crossOrigin", "Anonymous");
        mImg.src = reader.result;
        mImg.onload = () => {
          const canvas = document.createElement("canvas");
          canvas.width = mImg.naturalWidth;
          canvas.height = mImg.naturalHeight;
          const ctx = canvas.getContext("2d");
          ctx.drawImage(mImg, 0, 0);
          ctx.fillStyle = "white";
          ctx.textBaseline = "middle";
          let remFontSize = canvas.width / 35;
          if (remFontSize > 50) {
            remFontSize = 50;
          }
          ctx.font = `bold ${remFontSize}px Arial`;
          ctx.fillText(pText, 50, canvas.height - 150);

          ctx.fillText(pText1.substring(0,16), 50, canvas.height - 100);

          ctx.fillText(pText1.substring(16), 50, canvas.height - 50);
          // canvas.toBlob(resolve);
          canvas.toBlob((blob) => {
            resolve(blob);
          }, pFile.type, 0.5);
        };
      };
    }
  });
}