<template>
    <view class="bg-white" style="height: 100vh;">
        <!-- 视频播放器 -->
        <video style="width: 100%; height: 90%; border-radius: 5px;" :src='previewUrl' controls>
        </video>
    </view>
</template>

<script>
export default {
    data() {
        return {
            previewUrl: "",
        }
    },
    onLoad: async function () {
        try {
            const urlParams = this.$Route.query
            console.log("🚀 ~ onLoad ~ urlParams:", urlParams)

            // 检查参数
            if (!urlParams.file) {
                console.error('缺少文件参数');
                uni.showToast({
                    title: '缺少文件参数',
                    icon: 'none'
                });
                return;
            }

            console.log('开始请求预览URL...');
            let res = await this.$http.get('/pm/clockIn/previewPic?objectKey=' + urlParams.file)
            console.log('API响应:', res);

            if (res && res.data && res.data.result) {
                this.previewUrl = res.data.result;
                console.log('预览URL设置成功:', this.previewUrl);
            } else {
                console.error('API响应格式错误:', res);
                uni.showToast({
                    title: '获取预览失败',
                    icon: 'none'
                });
            }
        } catch (error) {
            console.error('onLoad错误:', error);
            uni.showToast({
                title: '加载失败: ' + error.message,
                icon: 'none'
            });
        }
    },
    methods: {
        share() {
            wx.onMenuShareTimeline({
                title: '', // 分享标题
                link: '', // 分享链接
                imgUrl: '', // 分享图标
                success: function () {
                    // 用户确认分享后执行的回调函数
                },
                cancel: function () {
                    // 用户取消分享后执行的回调函数
                }
            });
        }
    }
}
</script>

<style scoped>
.loading-container {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    height: 100%;
    color: #999;
    font-size: 16px;
}

/* 视频样式 */
video {
    object-fit: contain;
    background-color: #000;
}

/* 响应式调整 */
@media (max-width: 750rpx) {
    video {
        border-radius: 3px;
    }
}
</style>
