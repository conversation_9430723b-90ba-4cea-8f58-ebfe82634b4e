<template>
    <view class="bg-white" style="height: 100vh;">
        <!-- 视频播放器 -->
        <video style="width: 100%; height: 100%; border-radius: 5px;" :src='previewUrl' controls>
        </video>

        <!-- 分享按钮 -->
        <view class="share-button" @click="handleShare">
            <view class="share-icon">
                <text class="share-symbol">⤴</text>
            </view>
            <text class="share-text">分享</text>
        </view>
    </view>
</template>

<script>
export default {
    data() {
        return {
            previewUrl: "",
        }
    },
    onLoad: async function () {
        try {
            const urlParams = this.$Route.query
            console.log("🚀 ~ onLoad ~ urlParams:", urlParams)

            // 检查参数
            if (!urlParams.file) {
                console.error('缺少文件参数');
                uni.showToast({
                    title: '缺少文件参数',
                    icon: 'none'
                });
                return;
            }

            console.log('开始请求预览URL...');
            let res = await this.$http.get('/pm/clockIn/previewPic?objectKey=' + urlParams.file)
            console.log('API响应:', res);

            if (res && res.data && res.data.result) {
                this.previewUrl = res.data.result;
                console.log('预览URL设置成功:', this.previewUrl);
            } else {
                console.error('API响应格式错误:', res);
                uni.showToast({
                    title: '获取预览失败',
                    icon: 'none'
                });
            }
        } catch (error) {
            console.error('onLoad错误:', error);
            uni.showToast({
                title: '加载失败: ' + error.message,
                icon: 'none'
            });
        }
    },
    // 微信小程序分享配置
    onShareAppMessage() {
        return {
            title: '品牌推广视频',
            desc: '精彩的品牌推广内容，快来观看吧！',
            path: '/pages/brandPromotion/brandPromotionDetail?file=' + this.$Route.query.file,
            imageUrl: this.previewUrl || ''
        }
    },
    // 微信小程序分享到朋友圈
    onShareTimeline() {
        return {
            title: '品牌推广视频 - 精彩内容分享',
            query: 'file=' + this.$Route.query.file,
            imageUrl: this.previewUrl || ''
        }
    },
    methods: {
        // 处理分享点击事件
        handleShare() {
            // #ifdef MP-WEIXIN
            // 微信小程序分享
            this.shareToWeChat();
            // #endif

            // #ifdef APP-PLUS
            // App分享
            this.shareToApp();
            // #endif

            // #ifdef H5
            // H5分享
            this.shareToH5();
            // #endif
        },

        // 微信小程序分享
        shareToWeChat() {
            uni.showShareMenu({
                withShareTicket: true,
                success: () => {
                    console.log('分享菜单显示成功');
                },
                fail: (err) => {
                    console.error('分享菜单显示失败:', err);
                    uni.showToast({
                        title: '分享功能暂不可用',
                        icon: 'none'
                    });
                }
            });
        },

        // App分享
        shareToApp() {
            uni.share({
                provider: "weixin",
                scene: "WXSceneSession",
                type: 2,
                href: this.previewUrl,
                title: "品牌推广视频",
                summary: "精彩的品牌推广内容，快来观看吧！",
                imageUrl: this.previewUrl,
                success: (res) => {
                    console.log("分享成功:" + JSON.stringify(res));
                    uni.showToast({
                        title: '分享成功',
                        icon: 'success'
                    });
                },
                fail: (err) => {
                    console.log("分享失败:" + JSON.stringify(err));
                    uni.showToast({
                        title: '分享失败',
                        icon: 'none'
                    });
                }
            });
        },

        // H5分享
        shareToH5() {
            // 检查是否支持Web Share API
            if (navigator.share) {
                navigator.share({
                    title: '品牌推广视频',
                    text: '精彩的品牌推广内容，快来观看吧！',
                    url: window.location.href
                }).then(() => {
                    console.log('分享成功');
                }).catch((error) => {
                    console.log('分享失败:', error);
                    this.fallbackShare();
                });
            } else {
                // 降级处理
                this.fallbackShare();
            }
        },

        // 降级分享处理
        fallbackShare() {
            // 复制链接到剪贴板
            const url = window.location.href;
            if (navigator.clipboard) {
                navigator.clipboard.writeText(url).then(() => {
                    uni.showToast({
                        title: '链接已复制到剪贴板',
                        icon: 'success'
                    });
                });
            } else {
                // 显示分享选项
                uni.showActionSheet({
                    itemList: ['复制链接', '取消'],
                    success: (res) => {
                        if (res.tapIndex === 0) {
                            // 手动复制链接的提示
                            uni.showModal({
                                title: '分享链接',
                                content: url,
                                showCancel: false,
                                confirmText: '知道了'
                            });
                        }
                    }
                });
            }
        }
    }
}
</script>

<style scoped>
.loading-container {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    height: 100%;
    color: #999;
    font-size: 16px;
}

/* 视频样式 */
video {
    object-fit: contain;
    background-color: #000;
}

/* 分享按钮样式 */
.share-button {
    position: fixed;
    bottom: 100rpx;
    right: 40rpx;
    width: 120rpx;
    height: 120rpx;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 60rpx;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    box-shadow: 0 8rpx 20rpx rgba(102, 126, 234, 0.3);
    z-index: 999;
    transition: all 0.3s ease;
}

.share-button:active {
    transform: scale(0.95);
    box-shadow: 0 4rpx 12rpx rgba(102, 126, 234, 0.4);
}

.share-icon {
    width: 48rpx;
    height: 48rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 8rpx;
}

.share-symbol {
    font-size: 32rpx;
    color: #ffffff;
    font-weight: bold;
}

.share-text {
    font-size: 22rpx;
    color: #ffffff;
    font-weight: 500;
    line-height: 1;
}

/* 响应式调整 */
@media (max-width: 750rpx) {
    video {
        border-radius: 3px;
    }

    .share-button {
        bottom: 80rpx;
        right: 30rpx;
        width: 100rpx;
        height: 100rpx;
        border-radius: 50rpx;
    }

    .share-icon {
        width: 40rpx;
        height: 40rpx;
        margin-bottom: 6rpx;
    }

    .share-symbol {
        font-size: 28rpx;
    }

    .share-text {
        font-size: 20rpx;
    }
}
</style>
